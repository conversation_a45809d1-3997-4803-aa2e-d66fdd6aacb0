package com.facishare.crm.task.sfa.services.allocate;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.sfa.lto.utils.LeadsPoolUtil;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.model.AllocateRuleSortMember;
import com.facishare.crm.task.sfa.model.ApplicationMapping;
import com.facishare.crm.task.sfa.model.ObjectPoolPermission;
import com.facishare.crm.task.sfa.services.OrgServiceCommonService;
import com.facishare.crm.task.sfa.util.AllocateUtils;
import com.facishare.crm.task.sfa.util.LeadsUtil;
import com.facishare.crm.task.sfa.util.constant.AllocateConstants;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.UserInfoExt;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.enterpriserelation2.arg.GetOutUidArg;
import com.fxiaoke.enterpriserelation2.arg.GetOuterTenantIdByEaArg;
import com.fxiaoke.enterpriserelation2.arg.ListAllOuterTenantDownstreamByLinkAppIdArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.DownstreamLinkEmployeeVO;
import com.fxiaoke.enterpriserelation2.service.DownstreamService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.common.constants.CommonConstant.MIN_TIME_STAMP;
import static com.facishare.crm.task.sfa.util.constant.AllocateConstantsUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/1/23 15:15
 */

@Slf4j
@Component
public class AllocateMemberService {
    @Autowired
    private AllocateUtils allocateUtils;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AllocateMemberBiz allocateMemberBiz;
    @Autowired
    protected LeadsUtil leadsUtil;
    @Autowired
    protected OrgService orgService;
    @Autowired
    private OrgServiceCommonService orgServiceCommonService;
    @Autowired
    private AttendanceService AttendanceService;

    private PublicEmployeeService publicEmployeeService = SpringUtil.getContext().getBean(PublicEmployeeService.class);
    private FxiaokeAccountService fxiaokeAccountService = SpringUtil.getContext().getBean(FxiaokeAccountService.class);
    private DownstreamService downstreamService = SpringUtil.getContext().getBean(DownstreamService.class);


    private Map<String, String> outTenantMainOwnerMap = Maps.newHashMap();
    private List<DownstreamLinkEmployeeVO> outUserList = Lists.newArrayList();

    /**
     * 获取自动分配成员Id列表
     *
     * @param poolMemberIds 线索池成员列表
     * @return 排序过后的成员列表
     */
    public List<AllocateRuleSortMember> getMemberIds(User user, String apiName, List<String> poolMemberIds,
                                                     IObjectData allocateRule, IObjectData poolData,
                                                     String objectId, List<String> ownerHistoryIds) {
        String tenantId = user.getTenantId();
        String ruleId = allocateRule.get(ID).toString();
        Integer poolLimitCount = poolData.get(LIMIT_COUNT, Integer.class);
        String poolId = poolData.getId();
        String limitType = poolData.get(LIMIT_TYPE, String.class);
        List<AllocateRuleSortMember> sortMemberList = new ArrayList<>();
        // 获取规则下的成员
        List<IObjectData> allRuleMembers = allocateMemberBiz.getAllRuleMembers(ruleId, tenantId, poolData.getDescribeApiName());
        // 拍平所有分配成员
        List<String> flatAllMembers = flatAllMembers(user, allRuleMembers, allocateRule, poolMemberIds);

        log.info("objectId:{},ruleMembers:{} ,flatAllMembers:{}", objectId, allRuleMembers, flatAllMembers);

        // 拍平所有外部分配成员
        Map<String, List<String>> outEiEmployeeIdsMap = flatOutAllMembers(allRuleMembers, poolMemberIds, tenantId);

        List<String> outTenantIds = outEiEmployeeIdsMap.get("outTenantIds");
        List<String> outEmployeeIds = outEiEmployeeIdsMap.get("outEmployeeIds");

        log.info("all outAllMembers objectId:{}, poolMemberIds:{}, outTenantIds:{}, outEmployeeIds:{}",objectId, poolMemberIds,outTenantIds,outEmployeeIds);
        List<Map> outMemberCount = new ArrayList<>();
        if (CollectionUtils.notEmpty(outTenantIds) || CollectionUtils.notEmpty(outEmployeeIds)) {
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            outMemberCount = getOutMemberCount(tenantId, ea, poolId, limitType, outTenantIds, outEmployeeIds);
            // 添加外部成员
            flatAllMembers.addAll(outEmployeeIds);
        }
        // 获取成员分配历史
        List<IObjectData> allRuleMemberRecords = allocateMemberBiz.getAllRuleMemberRecords(ruleId, tenantId, apiName);
        // 已经分配的成员历史记录
        List<String> recordEmployeeIds = allRuleMemberRecords.stream()
                .filter(x -> x.get(EMPLOYEE_ID) != null && !"null".equalsIgnoreCase(x.get(EMPLOYEE_ID).toString()))
                .map(x -> x.get(EMPLOYEE_ID).toString()).collect(Collectors.toList());
        // 获取待分配成员列表
        List<String> nextAllocateMembers = getNextAllocateMembers(flatAllMembers, recordEmployeeIds);
        log.info("find next members tenantId:{},ruleId:{},leadsId:{},allMembers:{},recordHistory:{},nextAllocateMembers:{}", tenantId, ruleId, objectId, flatAllMembers, recordEmployeeIds, nextAllocateMembers);

        List<String> sortedEmpIds = removePoolLimit(tenantId, poolLimitCount, poolId, outMemberCount, nextAllocateMembers);
        log.info("remove limitCount,ruleId:{},leadsId:{},sortedEmpIds:{}", ruleId, objectId, sortedEmpIds);

        if (LeadsPoolUtil.isGraySfaAllocateSkipWorkFree(tenantId)) {
            if (poolData.get("skip_allocate_work_free") != null && poolData.get("skip_allocate_work_free", Boolean.class)) {
                sortedEmpIds = removeWorkFree(tenantId, objectId,nextAllocateMembers);
                log.info("remove workFree,ruleId:{},leadsId:{},sortedEmpIds:{}", ruleId, objectId, sortedEmpIds);
            }
        }

        sortedEmpIds = sortedEmpIds.stream().filter(x -> !ownerHistoryIds.contains(x)).collect(Collectors.toList());
        int order = 0;
        log.info("remove history,ruleId:{},leadsId:{},historyIds:{},sortedEmpIds:{}", ruleId, objectId, ownerHistoryIds, sortedEmpIds);
        for (String empId : sortedEmpIds) {
            Optional<IObjectData> record = allRuleMemberRecords.stream()
                    .filter(x -> x.get(ALLOCATE_RULE_ID).equals(ruleId) && x.get(EMPLOYEE_ID).equals(empId))
                    .findFirst();
            if (record.isPresent()) {
                sortMemberList.add(buildAllocateRuleSortMember(ruleId, empId, order, Long.parseLong(record.get().get(LAST_ALLOCATE_TIME).toString())));
            } else {
                sortMemberList.add(buildAllocateRuleSortMember(ruleId, empId, order, MIN_TIME_STAMP));
            }
            order++;
        }
        return sortMemberList;
    }

    @NotNull
    public List<String> removePoolLimit(String tenantId, Integer poolLimit, String poolId, List<Map> outMemberCount, List<String> nextAllocateMembers) {
        List<Map> allMemberCount = leadsUtil.getLeadsPoolCountByPoolIdAndOwner(tenantId, nextAllocateMembers, Lists.newArrayList(poolId));
        if (CollectionUtils.notEmpty(outMemberCount)) {
            allMemberCount.addAll(outMemberCount);
        }
        List<Map> finalAllMemberCountN = allMemberCount;
        //排除 线索池到达上限员工 不改变顺序
        List<String> sortedEmpIds = nextAllocateMembers.stream().filter(
                x -> finalAllMemberCountN.stream().noneMatch(
                        y -> y.get(OWNER).toString().equals(x) && Integer.parseInt(y.get(COUNT).toString()) >= poolLimit
                )
        ).collect(Collectors.toList());
        return sortedEmpIds;
    }

    @NotNull
    private List<String> getNextAllocateMembers(List<String> flatAllMembers, List<String> recordEmployeeIds) {
        if (CollectionUtils.empty(flatAllMembers)) {
            return new ArrayList<>();
        }
        // 新成员中，被分配的历史
        int recordsMemberIds = 0;
        // 分配历史从后向前 找到最后一个人是否还在新的成员里,如果在则
        for (int i = recordEmployeeIds.size() - 1; i >= 0; i--) {
            if (flatAllMembers.contains(recordEmployeeIds.get(i))) {
                recordsMemberIds = flatAllMembers.indexOf(recordEmployeeIds.get(i));
                if (flatAllMembers.size() > recordsMemberIds) {
                    recordsMemberIds++;
                    break;
                }
            }
        }
        // 取余，如果大于的时候从0开始，
        int next = recordsMemberIds % flatAllMembers.size();
        // 余下的待分配的成员
        List<String> nextAllocateMembers = flatAllMembers.stream().skip(next).limit(flatAllMembers.size()).collect(Collectors.toList());
        if (next != 0) {
            nextAllocateMembers.addAll(flatAllMembers.stream().limit(next).collect(Collectors.toList()));
        }
        return nextAllocateMembers;
    }

    /**
     * 拍平所有待分配成员, 过滤非线索池成员
     *
     * @param allRuleMembers
     * @param allocateRule
     * @param poolMemberIds
     * @param user
     */
    public List<String> flatAllMembers(User user, List<IObjectData> allRuleMembers, IObjectData allocateRule, List<String> poolMemberIds) {
        List<String> allUserIds = new ArrayList<>();
        // 规则中配置的员工id，后面单独去除停用。
        for (IObjectData member : allRuleMembers) {
            if (member.get(MEMBER_ID) == null) {
                continue;
            }
            String memberId = member.get(MEMBER_ID).toString();
            if (member.get(MEMBER_TYPE).toString().equals(AllocateConstants.AllocateMemberType.Circle.getValue())) {
                List<String> deptMemberIds = orgService.getMembersByDeptIds(user, Lists.newArrayList(memberId), 0);
                if (CollectionUtils.notEmpty(deptMemberIds)) {
                    allUserIds.addAll(deptMemberIds);
                }
            } else if (member.get(MEMBER_TYPE).toString().equals(AllocateConstants.AllocateMemberType.Employee.getValue())) {
                allUserIds.add(memberId);
            } else if (member.get(MEMBER_TYPE).toString().equals(AllocateConstants.AllocateMemberType.UserGroup.getValue())) {
                List<String> memberIdsByGroupIds = orgServiceCommonService.getMembersByGroupIds(user.getTenantId(), Lists.newArrayList(memberId));
                if (CollectionUtils.notEmpty(memberIdsByGroupIds)) {
                    allUserIds.addAll(memberIdsByGroupIds);
                }
            } else if (member.get(MEMBER_TYPE).toString().equals(AllocateConstants.AllocateMemberType.UserRole.getValue())) {
                Set<String> memberIdsByRoleIds = orgServiceCommonService.getUserIdsByRoleIds(user.getTenantId(), Lists.newArrayList(memberId));
                if (CollectionUtils.notEmpty(memberIdsByRoleIds)) {
                    allUserIds.addAll(memberIdsByRoleIds);
                }
            }
        }
        List<UserInfoExt> stopedUsers = orgService.getUserExtByIds(user.getTenantId(), user.getUserId(), allUserIds);
        stopedUsers.removeIf(x -> x.isActive() == true);
        List<String> stopedUserIds = stopedUsers.stream().map(x -> x.getId()).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(stopedUserIds)) {
            allUserIds.removeIf(x -> stopedUserIds.contains(x));
        }

        // 处理成员过滤条件
        List<String> customMemberIds = getCustomMemberIds(user, allocateRule);
        if (CollectionUtils.notEmpty(customMemberIds)) {
            allUserIds.addAll(customMemberIds);
        }
        allUserIds.removeIf(x -> !poolMemberIds.contains(x));
        allUserIds = allUserIds.stream().distinct().collect(Collectors.toList());
        if (GrayUtils.allocateUserSortById(user.getTenantId())) {
            allUserIds.sort(Comparator.naturalOrder());
        }
        return allUserIds;
    }

    private Map<String, List<String>> flatOutAllMembers(List<IObjectData> allRuleMembers, List<String> poolMemberIds, String tenantId) {
        Map<String, List<String>> map = new HashMap<>();

        List<String> outTenantIds = new ArrayList<>();
        List<String> outEmployeeIds = new ArrayList<>();
        for (IObjectData member : allRuleMembers) {
            if (member.get("member_id") == null){
                continue;
            }
            String memberId = member.get("member_id").toString();
            if (member.get("member_type").toString().equals(AllocateConstants.AllocateMemberType.OutTenant.getValue())) {
                outTenantIds.add(memberId);
            } else if (member.get("member_type").toString().equals(AllocateConstants.AllocateMemberType.OutEmployee.getValue())) {
                outEmployeeIds.add(memberId);
            }
        }
        outTenantIds.removeIf(x -> !poolMemberIds.contains(x));
        outEmployeeIds.removeIf(x -> !poolMemberIds.contains(x));
        map.put("outTenantIds", outTenantIds);
        outEmployeeIds = allocateUtils.getOutTenantsByOutUserIds(tenantId, outEmployeeIds);
        map.put("outEmployeeIds", outEmployeeIds);
        return map;
    }

    public List<Map> getOutMemberCount(String tenantId, String ea, String poolId, String limitType,
                                       List<String> outTenantIds, List<String> outEmployeeIds) {
        List<Map> result = Lists.newArrayList();
        Map<String, String> outTenantIdOwnerMap = Maps.newHashMap();
        HeaderObj headerObj = HeaderObj.newInstance("x_app_framework", Integer.valueOf(tenantId), null, null);
        if (ObjectPoolPermission.ObjectPoolLimitType.PERSONAL.getValue().equals(limitType)) {
            if (!outTenantIds.isEmpty()) {
                Long upstreamOutTenantId = getOutTenantId(ea, headerObj);
                for (String outTenantId : outTenantIds) {
                    if (outTenantIdOwnerMap.containsKey(outTenantId)) {
                        if (!outEmployeeIds.contains(outTenantIdOwnerMap.get(outTenantId))) {
                            outEmployeeIds.add(outTenantIdOwnerMap.get(outTenantId));
                        }
                        continue;
                    }
                    if (outTenantMainOwnerMap.containsKey(outTenantId)) {
                        outTenantIdOwnerMap.put(outTenantId, outTenantMainOwnerMap.get(outTenantId));
                        if (!outEmployeeIds.contains(outTenantMainOwnerMap.get(outTenantId))) {
                            outEmployeeIds.add(outTenantMainOwnerMap.get(outTenantId));
                        }
                    } else {
                        String mainOwner = getMainOwner(upstreamOutTenantId, outTenantId, headerObj);
                        if (!Strings.isNullOrEmpty(mainOwner)) {
                            outTenantMainOwnerMap.put(outTenantId, mainOwner);
                            outTenantIdOwnerMap.put(outTenantId, mainOwner);
                            if (!outEmployeeIds.contains(mainOwner)) {
                                outEmployeeIds.add(mainOwner);
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.notEmpty(outEmployeeIds)) {
                result = leadsUtil.getLeadsPoolCountByPoolIdAndOutOwner(tenantId, outEmployeeIds, poolId);
            }
        } else if (ObjectPoolPermission.ObjectPoolLimitType.ENTERPRISE.getValue().equals(limitType)) {
            if (!outEmployeeIds.isEmpty()) {
                if (CollectionUtils.empty(outUserList)) {
                    outUserList = getAllOutUser(ea, headerObj);
                }
                if (CollectionUtils.notEmpty(outUserList)) {
                    for (String employeeId : outEmployeeIds) {
                        if (outTenantIdOwnerMap.containsKey(employeeId)) {
                            if (!outTenantIds.contains(outTenantIdOwnerMap.get(employeeId))) {
                                outTenantIds.add(outTenantIdOwnerMap.get(employeeId));
                            }
                            continue;
                        }
                        Optional<DownstreamLinkEmployeeVO> employeeVO = outUserList.stream()
                                .filter(r -> Long.valueOf(employeeId).equals(r.getDownstreamOuterUid()))
                                .findFirst();
                        if (employeeVO.isPresent()) {
                            if (!outTenantIds.contains(String.valueOf(employeeVO.get().getDownstreamOuterTenantId()))) {
                                outTenantIds.add(String.valueOf(employeeVO.get().getDownstreamOuterTenantId()));
                            }
                            outTenantIdOwnerMap.put(employeeId, String.valueOf(employeeVO.get().getDownstreamOuterTenantId()));
                        }
                    }
                }
            }
            if (CollectionUtils.notEmpty(outTenantIds)) {
                result = leadsUtil.getLeadsPoolCountByPoolIdAndOutTenant(tenantId, outTenantIds, poolId);
            }
        }
        List<Map> toAddMap = Lists.newArrayList();
        if (CollectionUtils.notEmpty(result) && CollectionUtils.notEmpty(outTenantIdOwnerMap)) {
            for (Map.Entry<String, String> entry : outTenantIdOwnerMap.entrySet()) {
                Optional<Map> map = result.stream().filter(r -> entry.getValue().equals(r.get("owner"))).findFirst();
                if (map.isPresent()) {
                    Map newMap = Maps.newHashMap();
                    newMap.put("leads_pool_id", poolId);
                    newMap.put("owner", entry.getKey());
                    newMap.put("count", map.get().get("count"));
                    toAddMap.add(newMap);
                }
            }
            result.addAll(toAddMap);
        }
        return result;
    }

    private String getMainOwner(Long upstreamOutTenantId, String outTenantId, HeaderObj headerObj) {
        GetOutUidArg getOutUidArg = new GetOutUidArg();
        getOutUidArg.setDownstreamOuterTenantId(Long.valueOf(outTenantId));
        getOutUidArg.setUpstreamOuterTenantId(upstreamOutTenantId);
        RestResult<Long> mainOwnerResult = publicEmployeeService.getDownstreamRelationOwnerOuterUid(headerObj, getOutUidArg);
        if (mainOwnerResult != null) {
            return String.valueOf(mainOwnerResult.getData());
        }
        return null;
    }

    private Long getOutTenantId(String ea, HeaderObj headerObj) {
        GetOuterTenantIdByEaArg arg = new GetOuterTenantIdByEaArg();
        arg.setEa(ea);
        RestResult<Long> restResult = fxiaokeAccountService.getOuterTenantIdByEa(headerObj, arg);
        if (restResult != null) {
            return restResult.getData();
        }
        return null;
    }

    private List<DownstreamLinkEmployeeVO> getAllOutUser(String ea, HeaderObj headerObj) {
        ListAllOuterTenantDownstreamByLinkAppIdArg arg = new ListAllOuterTenantDownstreamByLinkAppIdArg();
        arg.setUpstreamEa(ea);
        arg.setLinkAppId(ApplicationMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
        RestResult<List<DownstreamLinkEmployeeVO>> downstreamEmpList = downstreamService.listAllOuterTenantDownstreamByLinkAppId(headerObj, arg);
        if (downstreamEmpList != null) {
            return downstreamEmpList.getData();
        }
        return Lists.newArrayList();
    }

    public List<String> getCustomMemberIds(User user, IObjectData allocateRule) {
        if (allocateRule.get(MEMBER_WHERES) == null) {
            return new ArrayList<>();
        }
        String memberWheres = allocateRule.get(MEMBER_WHERES).toString();
        List<String> dataIds = allocateMemberBiz.getCustomMemberIds(user, memberWheres);
        log.info("getCustomMemberIds allocateRuleId:{}, memberWheres:{}, customMemberIds:{}",allocateRule.getId(),memberWheres,dataIds);
        return dataIds;
    }

    public AllocateRuleSortMember buildAllocateRuleSortMember(String ruleId, String memberId, Integer order, Long allocateTime) {
        return AllocateRuleSortMember.builder()
                .allocateRuleId(ruleId)
                .memberId(memberId)
                .order(order)
                .lastAllocateTime(allocateTime)
                .build();
    }

    public List<String> removeWorkFree(String tenantId, String ObjectId, List<String> members) {
        if (!LeadsPoolUtil.isGraySfaAllocateSkipWorkFree(tenantId)) {
            return members;
        }
        Set<Integer> memberSet = members.stream().map(Integer::parseInt).collect(Collectors.toSet());
        Set<Integer> workTimeUserIds = AttendanceService.getWorkTimeUserIds(tenantId,ObjectId, memberSet, new Date());
        if (CollectionUtils.empty(workTimeUserIds)) {
            return Lists.newArrayList();
        }
        members.removeIf(x -> !workTimeUserIds.contains(Integer.parseInt(x)));
        return members;
    }
}
